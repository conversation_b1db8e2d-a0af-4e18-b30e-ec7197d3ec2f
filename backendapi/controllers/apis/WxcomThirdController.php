<?php

namespace backendapi\controllers\apis;

use common\components\wxcom\ThirdSDK;
use common\enums\StatusEnum;
use common\models\wxcom\AuthCom;
use common\models\wxcom\Suite;
use common\queues\RobotMessageJob;
use common\services\wxcom\DepartmentService;
use EasyWeChat\Factory;
use EasyWeChat\Work\Server\Handlers\EchoStrHandler;
use Exception;
use services\common\FeishuExamineService;
use Yii;
use yii\web\Controller;

class WxcomThirdController extends Controller
{
    public $enableCsrfValidation = false;
    public $suite;

    /**
     * 回调接口
     *
     * @return void
     */
    public function actionCallback($suiteCode, $corpId = null)
    {
        Yii::error(Yii::$app->request->get(), 'xxxxxxxxxxxxxxxxx-get');
        Yii::error(Yii::$app->request->post(), 'xxxxxxxxxxxxxxxxx-post');
        try {
            $suite = Suite::find()->where(['code' => $suiteCode, 'status' => StatusEnum::ENABLED])->one();
            if (!$suite || !$suite->provider) {
                return;
            }
            $this->suite = $suite;

            $options = [
                'corp_id' => $corpId ?: $suite->provider->corp_id,
                'secret' => $suite->provider->secret,
                'token' => $suite->token,
                'aes_key' => $suite->aes_key,
            ];

            $app = Factory::openWork($options);
            $server = $app->server;
            $server->push([$this, 'eventHandler']);
            $response = $server->serve();
            $response->sendContent();
        } catch (Exception $e) {
        }
    }

    public function eventHandler($message)
    {
        Yii::error($message, 'xxxxxxxxxxxxxxxxx-message');
        if ($message['InfoType']) {
            $this->eventHandlerForProvider($message);
        } else {
            $com = AuthCom::find()->where(['corp_id' => $message['ToUserName'], 'is_cancel' => 0, 'suite_id' => $this->suite->id])->one();
            $message['ComCode'] = $com->com->code;
            $this->eventHandlerForWxcom($message);
        }
    }

    public function eventHandlerForWxcom($message)
    {
        //数据回调
        switch ($message['Event']) {
                // 内部通讯录
            case 'change_contact':
                switch ($message['ChangeType']) {
                        // 部门变动
                    case 'update_party':
                    case 'create_party':
                    case 'delete_party':
                        DepartmentService::onEventCallback($message);
                        break;
                        // 成员变动
                    case 'update_user':
                    case 'create_user':
                    case 'delete_user':
                        \backendapi\forms\wxcom\UserForm::onEventCallback($message);
                        break;
                }
                break;
                // 客户标签
            case 'change_external_tag':
                \backendapi\forms\wxcom\CusTagForm::onEventCallback($message);
                break;
                // 外部通讯录（客户）
            case 'change_external_contact':
                switch ($message['ChangeType']) {
                    case 'add_external_contact':
                    case 'del_follow_user':
                    case 'del_external_contact':
                    case "add_half_external_contact":
                    case 'edit_external_contact':
                    case 'transfer_fail':
                        \backendapi\forms\wxcom\CusCustomerForm::onEventCallback($message);
                        break;
                }
                break;
        }
    }

    public function eventHandlerForProvider($message)
    {
        switch ($message['InfoType']) {
                // 保存ticket
            case 'suite_ticket':
                Yii::$app->redis->set("WxcomSuiteTicket:{$message['SuiteId']}", $message['SuiteTicket']);
                echo 'success';
                exit;
                // 发起授权
            case 'create_auth':
                $thirdSDK = new ThirdSDK($this->suite->suite_id, $this->suite->secret);
                $authInfo = $thirdSDK->getPermanentCode($message['AuthCode']);
                $com = AuthCom::find()->where(['corp_id' => $authInfo['auth_corp_info']['corpid'], 'is_cancel' => 0, 'suite_id' => $this->suite->id])->one();
                if (!$com) {
                    $com = new AuthCom();
                    $com->name = $authInfo['auth_corp_info']['corp_name'];
                    $com->corp_id = $authInfo['auth_corp_info']['corpid'];
                    $com->type = $authInfo['auth_corp_info']['corp_type'];
                    $com->logo_url = $authInfo['auth_corp_info']['corp_round_logo_url'];
                    $com->user_max = $authInfo['auth_corp_info']['corp_user_max'];
                    $com->subject_type = $authInfo['auth_corp_info']['subject_type'];
                    $com->wx_qrcode = $authInfo['auth_corp_info']['corp_wxqrcode'];
                    $com->scale = $authInfo['auth_corp_info']['corp_scale'];
                    $com->industry = $authInfo['auth_corp_info']['corp_industry'];
                    $com->sub_industry = $authInfo['auth_corp_info']['corp_sub_industry'];
                    $com->permanent_code = $authInfo['permanent_code'];
                    $com->wxcom_user_id = $authInfo['auth_user_info']['userid'];
                    $com->open_user_id = $authInfo['auth_user_info']['open_userid'];
                    $com->user_name = $authInfo['auth_user_info']['name'];
                    $com->user_avatar = $authInfo['auth_user_info']['avatar'];
                    $com->suite_id = $this->suite->id;
                    $com->provider_id = $this->suite->provider_id;
                    if (!$com->save()) {
                        echo $com->getFirstErrMsg();
                    }
                }
                break;
                // 重置授权码
            case 'reset_permanent_code':
                $thirdSDK = new ThirdSDK($this->suite->suite_id, $this->suite->secret);
                $authInfo = $thirdSDK->getPermanentCode($message['AuthCode']);
                $com = AuthCom::find()->where(['corp_id' => $authInfo['auth_corp_info']['corpid'], 'is_cancel' => 0, 'suite_id' => $this->suite->id])->one();
                if ($com) {
                    $com->permanent_code = $authInfo['permanent_code'];
                    if (!$com->save()) {
                        echo $com->getFirstErrMsg();
                    }
                }
                // 取消授权
            case 'cancel_auth':
                $com = AuthCom::find()->where(['corp_id' => $message['AuthCorpId'], 'is_cancel' => 0, 'suite_id' => $this->suite->id])->one();
                if ($com) {
                    $com->is_cancel = 1;
                    $com->save();
                }
                break;
        }
    }

    /**
     * 回调接口配置验证
     */
    public function actionVerify($suiteCode, $corpId = null)
    {
        $echostr = urldecode(Yii::$app->request->get('echostr'));
        if (!$echostr) {
            return;
        }

        $suite = Suite::find()->where(['code' => $suiteCode, 'status' => StatusEnum::ENABLED])->one();
        if (!$suite || !$suite->provider) {
            return;
        }

        $options = [
            'corp_id' => $corpId ?: $suite->provider->corp_id,
            'secret' => $suite->provider->secret,
            'token' => $suite->token,
            'aes_key' => $suite->aes_key,
        ];
        $app = Factory::work($options);
        $echoObject = (new EchoStrHandler($app))->handle();
        echo $echoObject->content;
        exit;
    }

    /**
     * 消息回调-workTool pro
     */
    public function actionMsgCallback()
    {
        $postData = Yii::$app->request->post();
        $group = FeishuExamineService::arrGroup('WECOMBOT');
        Yii::$app->feishuNotice->text($postData, $group['chat_id']);
        RobotMessageJob::addjob($postData);

        $result = [
            'code' => 0,
            'message' => 'success',
        ];

        echo json_encode($result, 256);
        exit;
    }
}
