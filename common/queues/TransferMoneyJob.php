<?php

namespace common\queues;

/**
 * 推广定时充值
 *
 * Class TransferMoneyJob
 */

use common\models\Config;
use backendapi\services\promote\TransferMoneyBatchService;
use common\helpers\BcHelper;
use common\models\backend\Member;
use Exception;
use services\common\FeishuExamineService;
use Yii;
use GuzzleHttp\Client;

class TransferMoneyJob extends BaseJob
{
    //延迟时间：单位秒，默认10
    public $delay = 10;
    //重试次数
    public $retryTimes = 1;
    //数据
    public $data;
    //是否发送消息
    public $isSendMessage = true;

    public function run($queue)
    {
        try {
            $data = $this->data;
            $model = new TransferMoneyBatchService();
            $res = $model->execute($data);
            $arrError = $model->resRealData($res);
        } catch (Exception $e) {
            $arrError[422] = ['422' => $e->getMessage()];
        }
        $this->sendMsg($arrError);
        return true;
    }

    public static function addJob(array $data)
    {
        $data['create_time'] = time();
        $que = Yii::$app->que;
        $job = new static([
            'data' => $data
        ]);

        if (!$data['isTimeRecharge']) {
            $que->setImportant();
        }

        if ($que->has($job)) {
            return true;
        }

        $delay = BcHelper::sub($data['execute_time'], $data['create_time']);
        $que->delay($delay)->push($job);
    }

    public static function addFansJob($sub_advertiser_id)
    {
        $transferAccount = Config::getByName('transferAccount');
        $transferAccount = explode("\n", $transferAccount);

        if (empty($sub_advertiser_id) || !in_array($sub_advertiser_id, $transferAccount)) {
            return false;
        }

        $transferData = [
            'target_advertiser_ids' => [$sub_advertiser_id],
            'amount' => 50,
            'user_name' => '系统自动充值',
        ];

        $que = Yii::$app->que;
        $job = new static([
            'data' => $transferData,
            'isSendMessage' => false
        ]);

        if ($que->has($job)) {
            return true;
        }

        if (!self::checkTransferMoneyCount($sub_advertiser_id)) {
            return false;
        }

        $que->push($job);
    }

    /**
     * 检查单个账户在一分钟内的充值次数,不能超过5次，且当天不允许在充值
     */
    public static function checkTransferMoneyCount($sub_advertiser_id)
    {
        $redis = Yii::$app->cache;
        $dayKey = 'AddFansTransferMoneyCount:' . date('Y-m-d');
        $dayAccount = $redis->get($dayKey);
        if ($dayAccount && in_array($sub_advertiser_id, $dayAccount)) {
            return false;
        }

        $redisKey = 'AddFansTransferMoneyCount:' . $sub_advertiser_id;

        $count = $redis->get($redisKey);

        if ($count === false) {
            $count = 1;
        } else {
            // 如果已存在，检查是否超过5次
            $count = intval($count);
            $count = intval($count) + 1;
            if ($count > 5) {
                $dayDelayTime = strtotime(date('Y-m-d')) + 86400 - time();
                $dayAccount[] = $sub_advertiser_id;
                $redis->set($dayKey, $dayAccount, $dayDelayTime);

                $group = FeishuExamineService::arrGroup('GGGLGTQ');
                $error = '账户ID：' . $sub_advertiser_id . PHP_EOL;
                $error .= '加粉异常,在一分钟内充值超过5次，已被限制充值';
                Yii::$app->feishuNotice->text($error, $group['chat_id']);
                return false;
            }
        }
        $redis->set($redisKey, $count, 5 * 60);
        return true;
    }

    public function sendMsg($arrError)
    {
        if (!$this->isSendMessage) {
            return false;
        }
        $content = '';
        $index = 0;
        $total = count($arrError);
        $is_success = false;
        foreach ($arrError as $code => $value) {
            if ($index > 0 && $index < $total) {
                $content .= PHP_EOL;
            }
            if ($code == 200) {
                $is_success = true;
                continue;
            }
            if ($code == 201) {
                $group = FeishuExamineService::arrGroup('GGGLGTQ');
                Yii::$app->feishuNotice->text($value['msg'], $group['chat_id']);
                $is_success = true;
                continue;
            }

            $content .= $value['msg'];
        }
        $sendUnionId = static::getSendUnionId($this->data['user_name']);
        $msg = '';
        $type = $this->data['isTimeRecharge'] ? '定时充值' : '推广充值';
        $title = $type;
        $title_bg = 'green';
        if (empty($content) && $is_success) {
            if (!$this->data['isTimeRecharge']) {
                return true;
            }
            $title .= '成功';
            $msg = '充值账户：' . implode(',', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'];
        }

        if ($content && $is_success) {
            $title .= '-部分充值成功';
            $title_bg = 'blue';
            $msg = '充值账户：' . implode(',', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'] . '<br/>';
            $msg .= '充值失败：' . '<br/>' . $content;
        }

        if ($content && !$is_success) {
            $title .= '失败';
            $title_bg = 'red';
            $msg = '充值账户：' . implode(',', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'] . '<br/>';
            $msg .= '充值失败：' . '<br/>' . $content;
        }

        if ($sendUnionId && $msg) {
            static::sendMsgToProcess($title, $msg, $title_bg, $sendUnionId);
        } else {
            $msg .= PHP_EOL . '操作人：' . $this->data['user_name'];
            Yii::$app->feishuNotice->text($msg);
        }
        return true;
    }

    public static function getSendUnionId($user_name)
    {
        $user_id = Member::find()
            ->select('id')
            ->where(['username' => $user_name])->scalar();
        if (empty($user_id)) {
            return '';
        }

        return \common\services\feishu\UserService::getPromoterUnionIdByID($user_id);
    }

    public static function sendMsgToProcess($title, $content, $title_bg, $union_id)
    {
        $url = "https://open.feishu.cn/anycross/trigger/callback/MDdkYmE4YjY3NmEzMmY1YjQzMWRkMDYwNjRlYTE3ZmM1?title={$title}&content={$content}&title_bg={$title_bg}&unionid={$union_id}";
        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
        ]);
        return json_decode($response->getBody()->getContents(), true);
    }
}
