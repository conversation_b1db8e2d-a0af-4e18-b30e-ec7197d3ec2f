<?php

namespace services\common;

use common\components\Feishu;
use common\components\Service;
use yii\base\Exception;
use common\components\feishu\FeishuRobotNotice;
use common\components\feishu\process\ProcessBase;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\Tool;
use Yii;

/**
 * Class PayService
 * @package services\common
 *
 */
class FeishuExamineService extends Service
{
    /**
     * 飞书发送工作通知
     *
     * @param array $data
     * @param array $feishu_user_ids
     * @return bool
     * @throws \Exception
     */
    public static function sendFeishu($msg, $feishu_user_id, $msg_type = 'text', $receive_id_type = 'user_id')
    {
        $feishu = new Feishu();
        if ($feishu_user_id) $feishu->sendWorkNotice($msg, $feishu_user_id, $msg_type, $receive_id_type);
        return true;
    }

    /**
     * 处理飞书审批流程
     */
    public static function getSingleApproval($instance_id)
    {
        $feishu = new Feishu();
        $result = $feishu->getSingleApproval($instance_id);

        $processBase = new ProcessBase([]);
        return ['data' => $processBase->dealData($result)];
    }

    /**
     * 多维表格回调-数据处理
     */
    public function callbackMoreTableDealData($callbackData)
    {
        if (empty($callbackData)) {
            return true;
        }

        $app_token = $callbackData['app_token'];
        $table_id = $callbackData['table_id'];
        $record_id = $callbackData['record_id'];

        $feishu = new Feishu();
        $dayData = $feishu->getMoreTableSingleData($app_token, $table_id, $record_id);

        if ($dayData['code'] != 0) {
            return false;
        }

        $currentDate = $dayData['data']['record']['fields']['日期'] / 1000;
        $year = date('Y', $currentDate);
        $month = date('m', $currentDate);

        //获取整个月的数据
        $date = DateHelper::aMonth($year, $month);
        $startTime = DateHelper::toDate($date['start'], 'Y-m-d');
        $endTime = DateHelper::toDate($date['end'], 'Y-m-d');
        $monthData = $this->getMoreTableData($app_token, $table_id, $startTime, $endTime);

        if ($monthData) {
            $changeData = [
                '当月完成业绩' => 0,
                '当月新客数' => 0,
                '当月新客业绩' => 0,
                '当月老客业绩' => 0,
                '当月老客人次数' => 0,
            ];
            $overDate = [];
            foreach ($monthData as $item) {
                if ($dayData['data']['record']['fields']['日期'] >= $item['fields']['日期']) {
                    $changeData['当月完成业绩'] += $item['fields']['今日完成业绩'] ?: 0;
                    $changeData['当月新客数'] += $item['fields']['今日新客数'] ?: 0;
                    $changeData['当月新客业绩'] += $item['fields']['今日新客业绩'] ?: 0;
                    $changeData['当月老客业绩'] += $item['fields']['今日老客业绩'] ?: 0;
                    $changeData['当月老客人次数'] += $item['fields']['今日老客数'] ?: 0;

                    if ($dayData['data']['record']['record_id'] != $item['record_id']) {
                        $overDate[] = [
                            '日期' => $item['fields']['日期'],
                            'record_id' => $item['record_id']
                        ];
                    }
                } else {
                    $overDate[] = [
                        '日期' => $item['fields']['日期'],
                        'record_id' => $item['record_id']
                    ];
                }
            }

            unset($item);
            $feishu->updateMoreTableData($app_token, $table_id, $record_id, $changeData);

            foreach ($overDate as $v) {
                $changeData = [
                    '当月完成业绩' => 0,
                    '当月新客数' => 0,
                    '当月新客业绩' => 0,
                    '当月老客业绩' => 0,
                    '当月老客人次数' => 0,
                ];

                foreach ($monthData as $item) {
                    if ($v['日期'] < $item['fields']['日期']) {
                        continue;
                    }
                    $changeData['当月完成业绩'] += $item['fields']['今日完成业绩'] ?: 0;
                    $changeData['当月新客数'] += $item['fields']['今日新客数'] ?: 0;
                    $changeData['当月新客业绩'] += $item['fields']['今日新客业绩'] ?: 0;
                    $changeData['当月老客业绩'] += $item['fields']['今日老客业绩'] ?: 0;
                    $changeData['当月老客人次数'] += $item['fields']['今日老客数'] ?: 0;
                }

                $feishu->updateMoreTableData($app_token, $table_id, $v['record_id'], $changeData);
            }
        }

        if (DateHelper::toDate($currentDate, 'Y-m-d') == DateHelper::toDate(time(), 'Y-m-d') && $callbackData['type'] == 'create') {
            //发送今日数据到群里
            $this->sendDataMsg($callbackData);

            $info = static::arrGroup($callbackData['store_code']);
            $data = [
                'dateTime' => $currentDate,
                'store_name' => $info['name'],
                'store_code' => $callbackData['store_code'],
                'amount' => BcHelper::sprintf($dayData['data']['record']['fields']['今日完成业绩']),
                'equalization' => BcHelper::sprintf($dayData['data']['record']['fields']['今日均产']),
            ];
            $this->sendRanking($data);
        }

        return true;
    }

    /**
     * 发送每日数据
     */
    public function sendDataMsg($callbackData)
    {
        $app_token = $callbackData['app_token'];
        $table_id = $callbackData['table_id'];
        $record_id = $callbackData['record_id'];
        $info = static::arrGroup($callbackData['store_code']);

        $feishu = new Feishu();
        $dayData = $feishu->getMoreTableSingleData($app_token, $table_id, $record_id);
        $arrData = $dayData['data']['record']['fields'];

        $store_name = $info['name'];
        $date =  DateHelper::toDate($arrData['日期'] / 1000, 'm月d日');
        $content = "<b>门店名称：" . $store_name . "</b>\n\n";
        $content .= "日期：" . $date . "\n";
        $content .= "今日完成业绩：" . BcHelper::sprintf($arrData['今日完成业绩']) . "\n";
        $content .= "今日新客数：" . $arrData['今日新客数'] . "\n";
        $content .= "今日均产：" . BcHelper::sprintf($arrData['今日均产']) . "\n";
        $content .= "今日新客业绩：" . BcHelper::sprintf($arrData['今日新客业绩']) . "\n";
        $content .= "今日新客客单价：" . BcHelper::sprintf($arrData['今日新客客单价']) . "\n";
        $content .= "今日老客业绩：" . $arrData['今日老客业绩'] . "\n";
        $content .= "今日老客数：" . $arrData['今日老客数'] . "\n";
        $content .= "今日老客客单价：" . BcHelper::sprintf($arrData['今日老客客单价']) . "\n";
        $content .= "当月完成业绩：" . $arrData['当月完成业绩'] . "\n";
        $content .= "当月新客数：" . $arrData['当月新客数'] . "\n";
        $content .= "当月均产：" . BcHelper::sprintf($arrData['当月均产']) . "\n";
        $content .= "当月新客业绩：" . $arrData['当月新客业绩'] . "\n";
        $content .= "当月新客客单价：" . BcHelper::sprintf($arrData['当月新客客单价']) . "\n";
        $content .= "当月老客业绩：" . $arrData['当月老客业绩']  . "\n";
        $content .= "当月老客人次数：" . $arrData['当月老客人次数'] . "\n";
        $content .= "当月老客客单价：" . BcHelper::sprintf($arrData['当月老客客单价']) . "\n";

        // Yii::$app->feishuNotice->text($content, FeishuRobotNotice::CATH_ID_STORE_MANAGER);
        Yii::$app->feishuNotice->text($content);
        return true;
    }

    /**
     * 获取多维表格中的数据
     */
    public function getMoreTableData($app_token, $table_id, $startTime, $endTime, $data = [], $page_token = '')
    {
        $filterQuery = '';
        if ($startTime && $endTime) {
            $filterQuery = 'CurrentValue.[日期] >= TODATE("' . $startTime . '")&&CurrentValue.[日期] <= TODATE("' . $endTime . '")';
        } elseif ($startTime) {
            $filterQuery = 'CurrentValue.[日期] >= TODATE("' . $startTime . '")';
        } elseif ($endTime) {
            $filterQuery = 'CurrentValue.[日期] <=TODATE("' . $endTime . '")';
        }
        $feishu = new Feishu();
        $res =  $feishu->getMoreTableData($app_token, $table_id, $filterQuery, $page_token, 31);
        if ($res['code'] != 0) {
            return [];
        }

        $list = $res['data']['items'] ?: [];
        $arrData = array_merge($data, $list);
        if ($res['data']['has_more']) {
            $page_token = $res['data']['page_token'];
            return $this->getMoreTableData($app_token, $table_id, $startTime, $endTime, $arrData, $page_token);
        }

        return $arrData;
    }

    /**
     * name      群名称
     * type      类型
     * store_manager  店长
     * form_url  多维表格表单填写地址
     * chat_id   机器人id
     */
    public static function arrGroup($key = '')
    {
        $group = [
            'WNXZ' => [
                'name' => '渭南新洲店',
                'type' => 'store',
                'store_manager' => [
                    'name' => '雒晓荣',
                    'feishu_user_id' => '9e4a8edc'
                ],
                'form_url' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnV9BaZO2M6VcQt3lU1K2F8b', //表单填写地址
                'chat_id' => 'oc_160f77b65a69e1e2c056f65d2021d5c5'
            ],
            'WNDL' => [
                'name' => '渭南大荔店',
                'type' => 'store',
                'store_manager' => [
                    'name' => '绳晓玉',
                    'feishu_user_id' => '644e44af'
                ],
                'form_url' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnLDBWrwFbbqZLdNZaI0dJog',
                'chat_id' => 'oc_561ef956d3dd6bb788c73d354836ff3e'
            ],
            'AKHC' => [
                'name' => '安康汉城店',
                'type' => 'store',
                'store_manager' => [
                    'name' => '陈芬',
                    'feishu_user_id' => '523bc7gb'
                ],
                'form_url' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcn2SLzaBX1GSdZ9sZTrePIic',
                'chat_id' => 'oc_117053f85d7423102e774d2c35379664'
            ],
            'XAQJ' =>  [
                'name' => '西安曲江店',
                'type' => 'store',
                'store_manager' => [
                    'name' => '',
                    'feishu_user_id' => ''
                ],
                'form_url' => '',
                'chat_id' => 'oc_7e4d92f45bb6294f2181ff478a059a7c'
            ],
            'WNPC' =>  [
                'name' => '渭南蒲城店',
                'type' => 'store',
                'store_manager' => [
                    'name' => '孟红杰',
                    'feishu_user_id' => 'cca36ef1'
                ],
                'form_url' => '',
                'chat_id' => 'oc_f10db0b7a6861d1f67f229f2919fd70c'
            ],
            // 'WNFP' =>  [
            //     'name' => '陕西渭南富平店',
            //     'type' => 'store',
            //     'store_manager' => [
            //         'name' => '白璐',
            //         'feishu_user_id' => 'd4eaegd6'
            //     ],
            //     'form_url' => '',
            //     'chat_id' => 'oc_e58bc23db62bcb45709eb1d6164ab297'
            // ],
            'DZQ' =>  [
                'name' => '店长群',
                'type' => 'system',
                'chat_id' => 'oc_1eef779a30b00db50c38be1987f14fad'
            ],
            'YJJBQ' =>  [
                'name' => '业绩捷报群',
                'type' => 'system',
                'chat_id' => 'oc_12538a7fa27f5136e4424835c74030a3'
            ],
            'XTXXQ' =>  [
                'name' => '系统信息群',
                'type' => 'system',
                'store_manager' => [
                    'name' => '林德志',
                    'feishu_user_id' => '92b12ec5'
                ],
                'form_url' => '',
                'chat_id' => 'oc_325afcd03502f0af8d1815dea69d32f9'
            ],
            'KFDJQ' =>  [
                'name' => '客服对接群',
                'type' => 'system',
                'chat_id' => 'oc_90019d84252e05ac338e09c7a1ef3947'
            ],
            'GGYYGTQ' =>  [ //外部群
                'name' => '广告运营群',
                'type' => 'system',
                'chat_id' => 'oc_c8665b7da4023c5266df8034a27a7d31'
            ],
            'KfDJQ2' =>  [
                'name' => '朋友用心交🙏父母拿命孝',
                'type' => 'system',
                'chat_id' => 'oc_1d731c6066d10a7a322f0ff8f9f0dac1'
            ],
            'YWXJGTQ' =>  [
                'name' => '业务衔接沟通群',
                'type' => 'system',
                'chat_id' => 'oc_010343d24f81a88a50c9bb8311dce340'
            ],
            'GGGLGTQ' => [
                'name' => '广告管理沟通群',
                'type' => 'system',
                'chat_id' => 'oc_1cba21fc948bbdf55bf8cfea9fb49d46'
            ],
            'JSGTQ' => [
                'name' => '技术沟通群',
                'type' => 'system',
                'chat_id' => 'oc_980a783b906f76933e3c930404909961'
            ],
            'XXXLYJQ' => [
                'name' => '信息泄露预警群',
                'type' => 'system',
                'chat_id' => 'oc_b6fe2fabe6044a795e9b58a38413d433'
            ],
            'WECOMBOT' => [
                'name' => '企业微信机器消息',
                'type' => 'system',
                'chat_id' => 'oc_83f5400534e771ca7e010b2c92df5018'
            ],
        ];
        if (empty($key)) {
            return $group;
        }

        if (!$group[$key]) {
            return [
                'name' => '未知群'
            ];
        }

        return $group[$key];
    }

    /**
     * 发送业绩捷报
     */
    public function sendRanking($data)
    {
        $storeCode = array_keys(FeishuExamineService::arrGroup());
        $key = 'todayStoreData:' . DateHelper::toDate($data['dateTime'], 'Y-m-d');
        $cacheData = Yii::$app->cache->get($key);
        if (in_array($data['store_code'], $storeCode)) {
            $cacheData[$data['store_code']] = $data;
            Yii::$app->cache->set($key, $cacheData, 86400);
        }

        $cacheDataKey = ArrayHelper::getColumn($cacheData, 'store_code');

        $count = count($storeCode);
        foreach ($storeCode as $code) {
            if (in_array($code, $cacheDataKey)) {
                $count--;
            }
        }

        if ($count) return true;

        //业绩排行
        $amountArr = $cacheData;
        $amountArrSort = array_column($amountArr, 'amount');
        array_multisort($amountArrSort, SORT_DESC, $amountArr);

        $amount_table = [];
        $i = 1;
        foreach ($amountArr as $item) {
            $amount_table[] = [
                'rank' => '第' . Tool::match($i) . '名',
                'store_name' => $item['store_name'],
                'value' => BcHelper::sprintf($item['amount'])
            ];
            $i++;
        }
        unset($item);

        //均产排行
        $equalizationArr = $cacheData;
        $equalizationArrSort = array_column($equalizationArr, 'equalization');
        array_multisort($equalizationArrSort, SORT_DESC, $equalizationArr);
        $equalization_table = [];
        $i = 1;
        foreach ($equalizationArr as $item) {
            $equalization_table[] = [
                'rank' => '第' . Tool::match($i) . '名',
                'store_name' => $item['store_name'],
                'value' => BcHelper::sprintf($item['equalization'])
            ];
            $i++;
        }
        unset($item);

        $content = [
            'type' => 'template',
            'data' => [
                'template_id' => 'ctp_AAgDipCeSodK', //业绩捷报-模版
                'template_variable' => [
                    'date' =>  DateHelper::toDate($data['dateTime'], 'm.d'),
                    'amount_table' => $amount_table,
                    'equalization_table' => $equalization_table
                ]
            ]
        ];
        sleep(1);
        // Yii::$app->feishuNotice->card($content, FeishuRobotNotice::CATH_ID_YJJBQ);
        Yii::$app->feishuNotice->card($content);
    }
}
